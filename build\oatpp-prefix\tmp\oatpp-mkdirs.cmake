# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file LICENSE.rst or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION ${CMAKE_VERSION}) # this file comes with cmake

# If CMAKE_DISABLE_SOURCE_CHANGES is set to true and the source directory is an
# existing directory in our source tree, calling file(MAKE_DIRECTORY) on it
# would cause a fatal error, even though it would be a no-op.
if(NOT EXISTS "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp")
  file(MAKE_DIRECTORY "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp")
endif()
file(MAKE_DIRECTORY
  "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp-build"
  "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix"
  "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/tmp"
  "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp-stamp"
  "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src"
  "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp-stamp"
)

set(configSubDirs )
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "C:/Users/<USER>/Desktop/CPP_APP/build/oatpp-prefix/src/oatpp-stamp${cfgdir}") # cfgdir has leading slash
endif()
