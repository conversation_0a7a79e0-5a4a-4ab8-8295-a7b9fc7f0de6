{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Test", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "Test::@6890427a1f51a3e7e1df", "jsonFile": "target-Test-Debug-5627803c181074414ad0.json", "name": "Test", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/CPP_APP/build", "source": "C:/Users/<USER>/Desktop/CPP_APP"}, "version": {"major": 2, "minor": 8}}