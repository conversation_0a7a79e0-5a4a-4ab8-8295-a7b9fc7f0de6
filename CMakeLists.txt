cmake_minimum_required(VERSION 3.10.0)
project(Test VERSION 0.1.0 LANGUAGES C CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Create the executable
add_executable(Test main.cpp)

# Since main.cpp only uses standard C++ libraries, no external dependencies are needed
# If you want to use oatpp in the future, uncomment and modify the following:
#
# include(ExternalProject)
# ExternalProject_Add(oatpp
#   GIT_REPOSITORY    https://github.com/oatpp/oatpp.git
#   GIT_TAG           1.3.1
#   CMAKE_ARGS        -DCMAKE_INSTALL_PREFIX=${CMAKE_BINARY_DIR}/oatpp
#                     -DOATPP_BUILD_TESTS=OFF
#                     -DCMAKE_BUILD_TYPE=Release
# )
#
# # Add dependency
# add_dependencies(Test oatpp)
#
# # Set include directories and link libraries after the external project
# target_include_directories(Test PRIVATE ${CMAKE_BINARY_DIR}/oatpp/include)
# target_link_directories(Test PRIVATE ${CMAKE_BINARY_DIR}/oatpp/lib)
# target_link_libraries(Test PRIVATE oatpp)
