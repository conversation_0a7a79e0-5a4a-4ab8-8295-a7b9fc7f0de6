cmake_minimum_required(VERSION 3.10.0)
project(Test VERSION 0.1.0 LANGUAGES C CXX)

# Add oatpp dependency
include(ExternalProject)
ExternalProject_Add(oatpp
  GIT_REPOSITORY    https://github.com/oatpp/oatpp.git
  GIT_TAG           1.3.0
  CMAKE_ARGS        -DCMAKE_INSTALL_PREFIX=${CMAKE_BINARY_DIR}/oatpp
                    -DOATPP_BUILD_TESTS=OFF
)

# Set include directories and libraries
include_directories(${CMAKE_BINARY_DIR}/oatpp/include)
link_directories(${CMAKE_BINARY_DIR}/oatpp/lib)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

add_executable(Test main.cpp)

# Add dependency
add_dependencies(Test oatpp)

# Link against oatpp libraries
target_link_libraries(Test oatpp)
