{"artifacts": [{"path": "Test.exe"}, {"path": "Test.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}], "language": "CXX", "sourceIndexes": [0]}], "id": "Test::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "Test", "nameOnDisk": "Test.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}