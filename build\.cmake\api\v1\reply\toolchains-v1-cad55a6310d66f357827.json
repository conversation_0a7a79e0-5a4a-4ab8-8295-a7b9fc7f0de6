{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include", "C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed", "C:/mingw810_64/x86_64-w64-mingw32/include"], "linkDirectories": ["C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0", "C:/mingw810_64/lib/gcc", "C:/mingw810_64/x86_64-w64-mingw32/lib", "C:/mingw810_64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "moldname", "mingwex", "pthread", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc", "moldname", "mingwex"]}, "path": "C:/mingw810_64/bin/gcc.exe", "version": "8.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++", "C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32", "C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward", "C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include", "C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed", "C:/mingw810_64/x86_64-w64-mingw32/include"], "linkDirectories": ["C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0", "C:/mingw810_64/lib/gcc", "C:/mingw810_64/x86_64-w64-mingw32/lib", "C:/mingw810_64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "moldname", "mingwex", "pthread", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc_s", "gcc", "moldname", "mingwex"]}, "path": "C:/mingw810_64/bin/g++.exe", "version": "8.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "C:/mingw810_64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}